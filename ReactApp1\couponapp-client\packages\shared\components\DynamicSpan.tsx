import React from 'react';
import { DynamicText } from '@repo/shared/lib/types/widgetSettings';
import { useDynamicExpression } from '../lib/dynamic-values';

interface DynamicSpanProps {
  dynamicText: DynamicText;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent<HTMLSpanElement>) => void;
  'data-editor-selectable-key'?: string;
}


export const DynamicSpan: React.FC<DynamicSpanProps> = ({
  dynamicText,
  className,
  style,
  onClick,
  'data-editor-selectable-key': dataEditorSelectableKey,
}) => {
  const renderedText = useDynamicExpression(dynamicText.expression, '');

  return (
    <span
      className={className}
      style={style}
      onClick={onClick}
      data-editor-selectable-key={dataEditorSelectableKey}
    >
      {renderedText}
    </span>
  );
};

export default DynamicSpan;
