import { useMemo } from 'react';
import { useLiveValues } from './useLiveValue';
import {
  renderExpression,
  renderExpressionWithVariables,
  extractVariablePaths,
  extractExpressionVariables,
  resolveExpressionVariablesInPath
} from '../expression-parser';
import { useExpressionVariables } from '../expression-variables';



/**
 * Enhanced hook to render dynamic text expressions with expression variable support
 * @param expression - The expression string with variables like "Score: {game/@currentGameId/score}"
 * @param fallbackValue - Value to use when a variable is not found
 * @returns Rendered string with variables replaced by live values
 */
export function useDynamicExpression(expression: string, fallbackValue: string = ''): string {
  const { resolveVariable, availableVariables } = useExpressionVariables();

  // Extract expression variables and resolve paths
  const resolvedPaths = useMemo(() => {
    const originalPaths = extractVariablePaths(expression);
    return originalPaths.map(path =>
      resolveExpressionVariablesInPath(path, resolveVariable)
    );
  }, [expression, availableVariables]); // Use availableVariables instead of resolveVariable

  // Subscribe to all resolved dynamic values
  const dynamicValues = useLiveValues(resolvedPaths);

  // Render the expression with expression variable resolution
  const renderedText = useMemo(() => {
    return renderExpressionWithVariables(
      expression,
      (path: string) => dynamicValues[path],
      resolveVariable,
      fallbackValue
    );
  }, [expression, dynamicValues, availableVariables, fallbackValue]); // Use availableVariables instead of resolveVariable

  return renderedText;
}
