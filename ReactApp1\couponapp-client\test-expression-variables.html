<!DOCTYPE html>
<html>
<head>
    <title>Test Expression Variables</title>
</head>
<body>
    <h1>Expression Variables Test</h1>
    <div id="test-results"></div>

    <script>
        // Simple test of the expression parser functions
        function testExpressionVariables() {
            const results = [];
            
            // Mock the functions (simplified versions)
            function extractExpressionVariablesFromPath(path) {
                const regex = /@[a-zA-Z][a-zA-Z0-9_]*/g;
                return path.match(regex) || [];
            }
            
            function resolveExpressionVariablesInPath(path, resolver) {
                return path.replace(/@[a-zA-Z][a-zA-Z0-9_]*/g, (match) => {
                    const resolved = resolver(match);
                    return resolved || match;
                });
            }
            
            // Test 1: Extract variables
            const path1 = "game/@currentGameId/score";
            const vars1 = extractExpressionVariablesFromPath(path1);
            results.push(`Test 1 - Path: ${path1}`);
            results.push(`Variables: ${JSON.stringify(vars1)}`);
            results.push(`Expected: ["@currentGameId"]`);
            results.push('');
            
            // Test 2: Resolve variables
            const resolver = (varName) => {
                if (varName === '@currentGameId') return '123456';
                if (varName === '@gameType') return 'quiz-game';
                return undefined;
            };
            
            const resolved = resolveExpressionVariablesInPath(path1, resolver);
            results.push(`Test 2 - Original: ${path1}`);
            results.push(`Resolved: ${resolved}`);
            results.push(`Expected: game/123456/score`);
            results.push('');
            
            // Test 3: Multiple variables
            const path2 = "game/@currentGameId/stats/@gameType";
            const vars2 = extractExpressionVariablesFromPath(path2);
            const resolved2 = resolveExpressionVariablesInPath(path2, resolver);
            results.push(`Test 3 - Path: ${path2}`);
            results.push(`Variables: ${JSON.stringify(vars2)}`);
            results.push(`Resolved: ${resolved2}`);
            results.push(`Expected variables: ["@currentGameId", "@gameType"]`);
            results.push(`Expected resolved: game/123456/stats/quiz-game`);
            
            return results;
        }
        
        // Run tests and display results
        const results = testExpressionVariables();
        document.getElementById('test-results').innerHTML = 
            '<pre>' + results.join('\n') + '</pre>';
    </script>
</body>
</html>
