import '@repo/shared/components/react-templates/widgetsImport'
import './widget-settings/widget-settings-registry'
import './property-controls/imports'

// Import necessary components and utilities
import { ConfigTypeProvider } from '@repo/shared/lib/game/configTypeContext'

import { Button } from '@repo/shared/components/ui/button' // Adjust the import path as needed
import { Popover, PopoverContent, PopoverTrigger } from '@repo/shared/components/ui/popover' // Adjust the import path based on your project structure
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@repo/shared/components/ui/dialog'
import { cn, createShortUuid } from '@repo/shared/lib/utils' // Utility function to create unique IDs
import { PopoverClose } from '@radix-ui/react-popover'
import {
    Box,
    ChevronRight,
    CopyIcon,
    DotSquareIcon,
    ImageIcon,
    LayoutGrid,
    MoreVerticalIcon,
    Play,
    Plus,
    PlusIcon,
    Rows,
    ShareIcon,
    TrashIcon,
    Type,
    ArrowUpIcon,
    ArrowDownIcon,
    ArrowLeftIcon,
} from 'lucide-react'
import React, { createContext, createRef, PropsWithChildren, Suspense, useCallback, useContext, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { atom, getDefaultStore, useAtom, useStore } from 'jotai'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { CampaignSceneType } from '@repo/shared/lib/types/campaign'
import { Label } from '@repo/shared/components/ui/label'
import { CSS } from '@dnd-kit/utilities'
import { useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { useOrganizationAssets } from '@repo/shared/lib/hooks/useCampaignAssets'
import { useCurrentOrganization } from '@repo/shared/lib/hooks/useCurrentOrganization'
// import { SettingField } from './settingsField'
import { sectionTemplates } from './sectionTemplates'
import { Updater } from 'use-immer'
import { ScrollArea } from '@repo/shared/components/ui/scroll-area'
import { Preset, PresetBlock, Widget, WidgetRenderingContext, WidgetTemplateSchema } from '@repo/shared/lib/types/editor'
import { EditorProvider, useEditor } from '@/lib/hooks/useEditor'
import { interpolate } from 'framer-motion'
import { VariablesEditor } from '@/components/editor/VariablesEditor'
import { useCampaignEditor } from '@/lib/hooks/useCampaignEditor'
import AddWidgetPopover from '@/Pages/campaign/editor/_components/addWidgetPopover'
import { getWidgetMetadata } from '@repo/shared/lib/widget-metadata'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { CursorProps, NodeApi, Tree } from 'react-arborist'
import { GripVertical } from 'lucide-react'
import { Input } from '@repo/shared/components/ui/input'
import { Separator } from '@repo/shared/components/ui/separator'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@repo/shared/components/ui/accordion'
import { getGameModule } from '@repo/shared/lib/game/gameRegistry'
import { GameAssetSelection } from '@repo/shared/lib/types/editorSelection'

// Import the atom family for storing game preview screen selection per widget
import { gamePreviewScreenAtomFamily, selectedEditorItemAtom } from '@repo/shared/lib/atoms/editor-atoms'
import { useWidgetSettings } from '@/lib/hooks/useWidgetSettings'
import { useCampaignData } from '@repo/shared/lib/hooks/useCampaignStore'
import { ControlType, getPropertyControlsNew } from '@repo/shared/components/editor/property-controls-new'
import { getPropertyControlHandler } from '../property-controls/propertyControlHandler'
import { useGameModule } from '@repo/shared/lib/hooks/useGameModule'
import { getConfigKeyDefinition, getConfigKeysByCategory } from '@repo/shared/lib/game/gameConfig'

// IframeWidgetRenderer component for CSS isolation
interface IframeWidgetRendererProps {
    context: WidgetRenderingContext
}

const IframeWidgetRenderer: React.FC<IframeWidgetRendererProps> = ({
    context
}) => {
    const iframeRef = useRef<HTMLIFrameElement>(null)
    const { campaignData } = useCampaignData()
    const store = useStore()
    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)

    useEffect(() => {
        console.log("Updating content",)

        if (iframeRef.current?.contentWindow) {
            //@ts-ignore
            iframeRef.current.contentWindow.window.context = context
            //@ts-ignore
            iframeRef.current.contentWindow.window.campaignData = campaignData
            //@ts-ignore
            iframeRef.current.contentWindow.window.editorAtomsStore = store

            const event = iframeRef.current.contentWindow.window.addEventListener('game-asset-select', (event: CustomEvent) => {
                setEditorSelection(event.detail)
            })

            //@ts-ignore
            iframeRef.current.contentWindow.window.update?.()

            return () => {
                if (iframeRef.current?.contentWindow) {
                    //@ts-ignore
                    iframeRef.current.contentWindow.window.removeEventListener('game-asset-select', event)
                }
            }
        }
    }, [context, campaignData, store, editorSelection])


    return (
        <iframe
            ref={iframeRef}
            src={`${window.location.pathname}/frame`}
            style={{
                width: '100%',
                height: '100%',
                borderRadius: '8px',
            }}
            title="Widget Renderer"
        />
    )
}

interface Template {
    id: string
    name: string
    template: string
    presets: Preset[]
    mainGroup: 'containers' | 'basic_elements'
}

interface GroupedTemplate {
    name: string
    templates: Template[]
}

interface AddWidgetPopoverProps {
    parentId: string
}

const syncRootWidgetWithNewItems = (root: Widget, newItems: TreeItemWidget[]): Widget => {
    // Create a map of all widgets in the current tree
    const widgetMap: Record<string, Widget> = {}
    const buildWidgetMap = (widget: Widget) => {
        widgetMap[widget.id] = widget
        widget.children.forEach(buildWidgetMap)
    }
    buildWidgetMap(root)

    // Recursive function to build the new tree structure
    const buildNewChildren = (items: TreeItemWidget[]): Widget[] => {
        return items
            .map((item) => {
                // Check if this is a FlexContainerWidget created from an artificial slot
                if ((item as any).isFlexContainerFromSlot) {
                    // Create a new FlexContainerWidget
                    return {
                        id: item.id,
                        componentName: "FlexContainerWidget",
                        displayName: item.name,
                        settings: {
                            flexDirection: "column",
                            justifyContent: "flex-start",
                            alignItems: "stretch",
                            gap: "8px",
                            backgroundColor: "transparent",
                            padding: "16px",
                            borderRadius: "8px",
                            height: "100%"
                        },
                        children: buildNewChildren(item.children)
                    }
                }

                const correspondingWidget = widgetMap[item.id]
                if (!correspondingWidget) {
                    console.warn(`Widget with id ${item.id} not found in rootWidget.`)
                    return null
                }

                return {
                    ...correspondingWidget,
                    children: buildNewChildren(item.children),
                }
            })
            .filter((widget): widget is Widget => widget !== null)
    }

    // Update the root widget with the new structure
    return {
        ...root,
        children: buildNewChildren(newItems),
    }
}

// Helper function to find all parent IDs of a widget in the tree
const findParentIds = (items: TreeItemWidget[], targetId: string, currentPath: string[] = []): string[] => {
    for (const item of items) {
        if (item.id === targetId) {
            return currentPath
        }
        if (item.children.length > 0) {
            const found = findParentIds(item.children, targetId, [...currentPath, item.id])
            if (found.length > 0) {
                return found
            }
        }
    }
    return []
}

// Define the shape of our context
interface SceneEditorContextType {
    selectedWidget: string | null
    setSelectedWidget: (widget: string | null) => void
    currentSceneId: string
}

// Create a context for the SceneEditor with a default value
const SceneEditorContext = createContext<SceneEditorContextType>({
    currentSceneId: null,
    selectedWidget: null,
    setSelectedWidget: () => { },
})

export const useSceneEditor = () => {
    const context = useContext(SceneEditorContext)
    if (!context) {
        throw new Error('useSceneEditor must be used within an SceneEditor')
    }
    return context
}

// Props type for the main SceneEditor component
interface SceneEditorProps {
    rootWidget?: Widget
    setRootWidget?: (rootWidget: Widget) => void
    currentSceneId: string
    children: React.ReactNode
}

// Main SceneEditor component
const SceneEditor: React.FC<SceneEditorProps> & {
    EditorView: React.FC
    WidgetTree: React.FC
    WidgetSettings: React.FC
} = ({ children, currentSceneId }) => {
    const [selectedWidget, setSelectedWidget] = useState<string | null>(null)

    const value: SceneEditorContextType = {
        currentSceneId,
        selectedWidget,
        setSelectedWidget,
    }

    return <SceneEditorContext.Provider value={value}>{children}</SceneEditorContext.Provider>
}

interface TreeItemWidget {
    id: string
    name: string
    children: TreeItemWidget[]
    isArtificalSlot?: boolean
    isFlexContainerFromSlot?: boolean
}

SceneEditor.WidgetTree = () => {
    const { rootWidget, setRootWidget, findWidgetById, editorSelection } = useEditor()
    const [treeItems, setTreeItems] = useState<TreeItemWidget[]>([])

    const mapWidgetToTreeItem = useCallback((widget: Widget, previousItems: TreeItemWidget[]): TreeItemWidget => {
        const previousTreeItem = previousItems.find((t) => t.id === widget.id)

        if (widget.componentName == "GameWidget") {
            console.log('Game widget: ', widget)

            // Get the game module to access widgetSlots
            const gameId = widget.settings?.gameId
            const gameModule = getGameModule(gameId)

            // Create children array starting with regular widget children
            const children: TreeItemWidget[] = widget.children.map((o) => mapWidgetToTreeItem(o, previousItems))

            // Add artificial slots if the game module has widgetSlots defined
            if (gameModule?.widgetSlots) {
                gameModule.widgetSlots.forEach((slot: { widgetId: string, displayName: string }) => {
                    // Check if a real widget with this slot's widgetId already exists
                    const existingWidget = widget.children.find(child => child.id === slot.widgetId)

                    // Only show artificial slot if no real widget exists with that ID
                    if (!existingWidget) {
                        children.push({
                            id: slot.widgetId,
                            name: slot.displayName,
                            children: [],
                            isArtificalSlot: true
                        })
                    }
                })
            }

            return {
                id: widget.id,
                name: widget.displayName ?? widget.componentName,
                children: children
            }
        }

        const widgetMetadata = getWidgetMetadata(widget?.componentName)

        return {
            id: widget.id,
            name: widget.displayName ?? widgetMetadata?.displayName ?? widget.componentName,
            children: widget.children.map((o) => mapWidgetToTreeItem(o, previousItems)),
        }
    }, [])

    useEffect(() => {
        if (!rootWidget?.children) {
            return
        }
        const mappedItems = rootWidget.children.map((o) =>
            mapWidgetToTreeItem(
                o,
                treeItems.flatMap((t) => [t, ...t.children])
            )
        )
        setTreeItems(mappedItems)
    }, [rootWidget, rootWidget?.children, mapWidgetToTreeItem])

    const updateRootWidgetStructure = (newTreeItems: TreeItemWidget[]) => {
        const updatedRoot = syncRootWidgetWithNewItems(rootWidget, newTreeItems)
        setRootWidget(updatedRoot)
    }

    // Helper function to find the parent of a tree item
    const findParentOfTreeItem = (items: TreeItemWidget[], targetId: string): TreeItemWidget | null => {
        for (const item of items) {
            if (item.children.some(child => child.id === targetId)) {
                return item
            }
            const foundInChildren = findParentOfTreeItem(item.children, targetId)
            if (foundInChildren) {
                return foundInChildren
            }
        }
        return null
    }

    const handleMove = ({ dragIds, parentId, index }) => {
        setTreeItems((items) => {
            const newItems = [...items]
            const draggedItems = dragIds.map((id) => findTreeItemById(items, id)).filter(Boolean)

            // Check if we're dropping on an artificial slot
            const targetParent = parentId ? findTreeItemById(newItems, parentId) : null
            const isDropOnArtificialSlot = targetParent?.isArtificalSlot

            // Remove items from their current position
            draggedItems.forEach((item) => {
                const { parentItems, index } = findItemAndParent(newItems, item.id)
                if (parentItems && index !== -1) {
                    parentItems.splice(index, 1)
                }
            })

            if (isDropOnArtificialSlot && targetParent) {
                // Handle artificial slot replacement
                // Find the GameWidget parent and replace the artificial slot with a real FlexContainerWidget
                const gameWidgetParent = findParentOfTreeItem(newItems, targetParent.id)
                if (gameWidgetParent) {
                    // Remove the artificial slot and add the real FlexContainerWidget tree item
                    const slotIndex = gameWidgetParent.children.findIndex((child: any) => child.id === targetParent.id)
                    if (slotIndex !== -1) {
                        gameWidgetParent.children.splice(slotIndex, 1, {
                            id: targetParent.id,
                            name: targetParent.name,
                            children: draggedItems,
                            isFlexContainerFromSlot: true // Mark this as a special FlexContainer created from slot
                        })
                    }
                }
            } else {
                // Normal drop handling
                if (parentId === null) {
                    // Add to root level
                    newItems.splice(index, 0, ...draggedItems)
                } else {
                    // Add to parent
                    const parent = findTreeItemById(newItems, parentId)
                    if (parent) {
                        parent.children.splice(index, 0, ...draggedItems)
                    }
                }
            }

            updateRootWidgetStructure(newItems)
            return newItems
        })
    }

    const findItemAndParent = (items: TreeItemWidget[], id: string): { parentItems: TreeItemWidget[] | null; index: number } => {
        // Check root level
        const rootIndex = items.findIndex((item) => item.id === id)
        if (rootIndex !== -1) {
            return { parentItems: items, index: rootIndex }
        }

        // Check nested levels
        for (const item of items) {
            const childIndex = item.children.findIndex((child) => child.id === id)
            if (childIndex !== -1) {
                return { parentItems: item.children, index: childIndex }
            }
            const result = findItemAndParent(item.children, id)
            if (result.parentItems) {
                return result
            }
        }

        return { parentItems: null, index: -1 }
    }

    const findTreeItemById = (items: TreeItemWidget[], id: string): TreeItemWidget | null => {
        for (const item of items) {
            if (item.id === id) return item
            if (item.children.length > 0) {
                const found = findTreeItemById(item.children, id)
                if (found) return found
            }
        }
        return null
    }

    function TreeNode({ node, style, dragHandle }: { node: NodeApi<TreeItemWidget>; style: any; dragHandle: any }) {
        const indent = node.level * 20
        const { duplicateWidget, deleteWidget, findWidgetById, rootWidget, selectWidget } = useEditor()
        const [isExportOpen, setIsExportOpen] = useState(false)
        const [exportData, setExportData] = useState('')

        const widget = findWidgetById(rootWidget, node?.data?.id)
        const widgetMetadata = getWidgetMetadata(widget?.componentName)
        const nodeName = node?.data.name ?? widgetMetadata?.displayName

        const handleExport = (widget: Widget) => {
            setExportData(JSON.stringify(widget, null, 2))
            setIsExportOpen(true)
        }

        const exportDialog = (
            <Dialog open={isExportOpen} onOpenChange={setIsExportOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>Export Widget</DialogTitle>
                    </DialogHeader>
                    <div className="mt-4">
                        <div className="relative">
                            <pre className="p-4 rounded-lg bg-muted overflow-auto max-h-[400px]">
                                <code>{exportData}</code>
                            </pre>
                            <Button
                                variant="outline"
                                size="icon"
                                className="absolute top-2 right-4"
                                onClick={() => {
                                    navigator.clipboard.writeText(exportData)
                                }}
                            >
                                <CopyIcon className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        )

        const selectNode = () => {
            node.toggle()
            console.log('Select widget 1')
            selectWidget(findWidgetById(rootWidget, node.data.id))
        }

        useEffect(() => {
            if (editorSelection?.widgetId && findParentIds(treeItems, editorSelection?.widgetId).includes(node.data.id) && !node.isOpen) {
                node.toggle()
            }
        }, [editorSelection])



        return (
            <>
                <div
                    ref={dragHandle}
                    style={{
                        ...style,
                        paddingLeft: `${indent}px`,
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                    }}
                    className="py-1 px-2 rounded-sm"
                >
                    <div className={`flex flex-grow rounded-sm items-stretch hover:bg-accent ${editorSelection?.widgetId === node.data.id ? 'bg-accent' : ''}`}>
                        <div className="flex items-center gap-2 flex-1  ps-2 py-2 text-sm " onClick={selectNode}>
                            {node.data.children?.length == 0 ? (
                                <div className="" />
                            ) : (
                                <ChevronRight
                                    className={cn('w-4 h-4 transition-transform', {
                                        'transform rotate-90': node.isOpen,
                                    })}
                                />
                            )}
                            <span>{nodeName}</span>
                        </div>
                        {/* Actions */}
                        <div className="flex gap-2">
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button variant="ghost" size="icon" className="justify-center items-center">
                                        <MoreVerticalIcon className="h-4 w-4" />
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-full px-1 py-1">
                                    <PopoverClose className="flex flex-col">
                                        <Button
                                            variant="ghost"
                                            className="w-full text-left"
                                            onClick={(e) => {
                                                e.preventDefault()
                                                duplicateWidget(node.data.id)
                                            }}
                                        >
                                            Duplicate
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            className="w-full text-left"
                                            onClick={(e) => {
                                                e.preventDefault()
                                                handleExport(findWidgetById(rootWidget, node.data.id))
                                            }}
                                        >
                                            Export
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            className="w-full text-left text-destructive"
                                            onClick={(e) => {
                                                e.preventDefault()
                                                deleteWidget(node.data.id)
                                            }}
                                        >
                                            Delete
                                        </Button>
                                    </PopoverClose>
                                </PopoverContent>
                            </Popover>
                        </div>
                    </div>
                </div>
                {exportDialog}
            </>
        )
    }

    const DragCursor = (props: CursorProps) => {
        return (
            <div
                className="h-[2px] bg-primary/50 w-full"
                style={{
                    position: 'absolute',
                    top: `${props.top}px`,
                    left: `${props.left}px`,
                    width: `100px`,
                }}
            ></div>
        )
    }

    return (
        <div className="space-y-2">
            <div className="widget-tree mt-4">
                <div className="flex flex-grow items-center justify-between border-b px-3 pb-2 mb-2">
                    <div className="font-bold text-sm">Widget tree</div>
                    <AddWidgetPopover parentId={null} />
                </div>
            </div>
            <Tree
                data={treeItems}
                openByDefault={false}
                width={'100'}
                onMove={handleMove}
                rowHeight={42}
                padding={8}
                renderCursor={DragCursor}
                disableDrop={(props) => {
                    if (props.parentNode.isRoot) return false

                    // Check if the target is an artificial slot
                    const targetTreeItem = findTreeItemById(treeItems, '' + props.parentNode.data.id)
                    if (targetTreeItem?.isArtificalSlot) {
                        // Allow dropping on artificial slots
                        return false
                    }

                    const widget = findWidgetById(rootWidget, '' + props.parentNode.data.id)
                    const metadata = getWidgetMetadata(widget?.componentName)
                    console.log('To widget: ', props.parentNode.data.id)
                    return metadata?.type !== 'container'
                }}
            >
                {/*@ts-ignore*/}
                {TreeNode}
            </Tree>
        </div>
    )
}

const SceneTypeSelector: React.FC<{
    value?: CampaignSceneType
    onChange: (value: CampaignSceneType) => void
}> = ({ value, onChange }) => {
    return (
        <div className="space-y-4">
            <Label>Page Type</Label>
            <Select value={value} onValueChange={onChange}>
                <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select page type..." />
                </SelectTrigger>
                <SelectContent>
                    {Object.values(CampaignSceneType).map((type) => (
                        <SelectItem key={type} value={type}>
                            {type
                                .split('_')
                                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                                .join(' ')}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    )
}

SceneEditor.WidgetSettings = () => {
    const { editorSelection, findWidgetById, rootWidget } = useEditor()
    const { scenes, setScenes } = useCampaignEditor()
    const { currentSceneId } = useSceneEditor()

    const selectedWidget = useMemo(() => findWidgetById(rootWidget, editorSelection?.widgetId), [editorSelection, rootWidget])
    const { settings } = useWidgetSettings(selectedWidget)
    const propertyControlsMetadata = useMemo(() => getPropertyControlsNew(selectedWidget?.componentName), [selectedWidget])
    const { settings: rootSettings } = useWidgetSettings(rootWidget)


    const handleSceneTypeChange = (type: CampaignSceneType) => {
        setScenes((draft) => {
            const currentSceneIndex = draft.findIndex((scene) => scene.id === currentSceneId)
            if (currentSceneIndex > -1) {
                draft[currentSceneIndex].type = type
            }
        })
    }

    if (editorSelection?.type == 'game-asset') {
        return (
            <div>
                <GameAssetSettingsSidebar />
            </div>
        )
    }

    if (!editorSelection) {
        console.log('WidgetSettings: No editorSelection, checking if we should show root widget settings')
        // If no widget is selected but we have a root widget, show root widget settings
        if (rootWidget) {
            console.log('WidgetSettings: Showing root widget settings as fallback')
            const rootPropertyControlsMetadata = getPropertyControlsNew(rootWidget?.componentName)

            const rootControls = rootPropertyControlsMetadata ? Object.entries(rootPropertyControlsMetadata)
                .filter(([, propertyControl]) => {
                    if (propertyControl.hidden && typeof propertyControl.hidden === 'function') {
                        return !propertyControl.hidden(rootSettings || {});
                    }
                    return true;
                })
                .map(([key, propertyControl]) => {
                    const ControlComponent = getPropertyControlHandler(propertyControl.type)
                    return {
                        key,
                        propertyControl,
                        ControlComponent
                    }
                }) : []

            return (
                <div>
                    <SceneTypeSelector value={scenes.find((scene) => scene.id === currentSceneId)?.type} onChange={handleSceneTypeChange} />
                    <div className="h-4" />
                    <VariablesEditor />

                    <div className="flex flex-col gap-2">
                        {rootControls.map(({ key, propertyControl, ControlComponent }) => (
                            <div key={key}>
                                {ControlComponent ? (
                                    <ControlComponent widget={rootWidget} property={key} />
                                ) : (
                                    <div>No handler found: {propertyControl.type}</div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            )
        }

        return (
            <div>
                <p className="text-secondary text-sm text-center">Select a widget to edit its properties.</p>
            </div>
        )
    }

    console.log('WidgetSettings: rerender widget settings', {
        editorSelection,
        selectedWidget,
        rootWidgetId: rootWidget?.id
    })


    const controls = useMemo(() => {
        if (!propertyControlsMetadata) return []

        return Object.entries(propertyControlsMetadata)
            .filter(([, propertyControl]) => {
                // Check if the property control should be hidden
                if (propertyControl.hidden && typeof propertyControl.hidden === 'function') {
                    return !propertyControl.hidden(settings || {});
                }
                return true;
            })
            .map(([key, propertyControl]) => {
                const ControlComponent = getPropertyControlHandler(propertyControl.type)
                return {
                    key,
                    propertyControl,
                    ControlComponent
                }
            })
    }, [propertyControlsMetadata, settings])

    return (
        <div>
            {editorSelection?.widgetId === rootWidget?.id && (
                <>
                    <SceneTypeSelector value={scenes.find((scene) => scene.id === currentSceneId)?.type} onChange={handleSceneTypeChange} />
                    <div className="h-4" />
                    <VariablesEditor />
                </>
            )}

            <div className="flex flex-col gap-2">
                {controls.map(({ key, propertyControl, ControlComponent }) => (
                    <div key={key}>
                        {key}
                        <div>
                            {ControlComponent ? (
                                <ControlComponent widget={selectedWidget} property={key} />
                            ) : (
                                <div>No handler found: {propertyControl.type}</div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}

const GameAssetSettingsSidebar = () => {
    const { editorSelection, updateWidgetSettings, findWidgetById, rootWidget, selectWidget } = useEditor()
    const assetSelection = editorSelection as GameAssetSelection

    const widget = useMemo(() => editorSelection?.widgetId ? findWidgetById(rootWidget, editorSelection?.widgetId) : null, [editorSelection, rootWidget])
    const { settings } = useWidgetSettings(widget)

    const { gameModule } = useGameModule(settings?.gameId)
    const SingleConfigEditComponent = useMemo(() => gameModule?.configKeyEditor, [gameModule])

    const updateConfig = (config: any) => {
        updateWidgetSettings(widget.id, 'gameConfig', 'desktop', config)
    }

    if (!settings) {
        return <>No settings found.</>
    }

    return (
        <div className="space-y-4">
            <Button
                variant="ghost"
                className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
                onClick={() => {
                    console.log('Select widget 3')
                    selectWidget(widget)
                }}
            >
                <ArrowLeftIcon className="h-4 w-4" />
                Back to Game Settings
            </Button>
            <div className="space-y-2">
                {SingleConfigEditComponent != null && (
                    <Suspense>
                        <ConfigTypeProvider configType={gameModule.configType}>
                            <SingleConfigEditComponent configKey={assetSelection.assetKey} config={settings.gameConfig} updateConfig={updateConfig} />
                        </ConfigTypeProvider>
                    </Suspense>
                )}
            </div>
        </div>
    )
}






// Modify SceneEditor.EditorView to include the type selector
SceneEditor.EditorView = () => {
    const { currentOrganization } = useCurrentOrganization()
    const { assetsData, isLoadingAssets, assetsError } = useOrganizationAssets(currentOrganization?.id)
    const { rootWidget, setRootWidget, editorSelection, selectWidget } = useEditor()
    const { viewMode } = useCampaignEditor()

    if (!rootWidget) return null

    const context: WidgetRenderingContext = {
        widget: rootWidget,
        widgetId: rootWidget.id,
        sceneId: 'just_preview',
        breakpoint: viewMode,
        editorContext: {
            isEditorMode: true,
            isSelectedInEditor: editorSelection?.widgetId === rootWidget.id,
            useEditorContext: useEditor(),
        }
    }

    if (isLoadingAssets) {
        return <>Loading...</>
    }

    return (
        <>
            <div className="flex flex-grow parent-editor">
                <IframeWidgetRenderer
                    context={context}
                />
            </div>


        </>
    )
}

export default SceneEditor
