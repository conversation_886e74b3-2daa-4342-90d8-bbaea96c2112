// Quick debug script to test expression variables
const { 
  extractExpressionVariablesFromPath,
  resolveExpressionVariablesInPath,
  extractExpressionVariables,
  parseExpression
} = require('./packages/shared/lib/dynamic-values/expression-parser.ts');

console.log('=== Testing Expression Variables ===');

// Test 1: Extract variables from path
const path1 = "game/@currentGameId/score";
console.log(`\n1. Testing path: ${path1}`);
const variables1 = extractExpressionVariablesFromPath(path1);
console.log(`   Variables found: ${JSON.stringify(variables1)}`);
// Expected: ["@currentGameId"]

// Test 2: Resolve variables in path
console.log(`\n2. Testing variable resolution:`);
const resolver = (varName) => {
  console.log(`   Resolving: ${varName}`);
  if (varName === '@currentGameId') return '123456';
  if (varName === '@gameType') return 'quiz-game';
  return undefined;
};

const resolvedPath = resolveExpressionVariablesInPath(path1, resolver);
console.log(`   Original: ${path1}`);
console.log(`   Resolved: ${resolvedPath}`);
// Expected: "game/123456/score"

// Test 3: Parse full expression
console.log(`\n3. Testing full expression parsing:`);
const expression = "Score: {game/@currentGameId/score}";
console.log(`   Expression: ${expression}`);
const parsed = parseExpression(expression);
console.log(`   Variables: ${JSON.stringify(parsed.variables)}`);
console.log(`   Expression Variables: ${JSON.stringify(parsed.expressionVariables)}`);
console.log(`   Segments: ${JSON.stringify(parsed.segments, null, 2)}`);

// Test 4: Extract expression variables from expression
const expressionVars = extractExpressionVariables(expression);
console.log(`   Expression variables extracted: ${JSON.stringify(expressionVars)}`);

console.log('\n=== Test Complete ===');
